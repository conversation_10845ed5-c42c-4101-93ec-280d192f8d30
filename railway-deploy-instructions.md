# 🚂 Railway Deploy - Instrucciones Paso a Paso

## ✅ Todo está listo para el deploy

He preparado todo automáticamente para ti. Sigue estos pasos simples:

### 📋 Paso 1: Acceder a Railway
- ✅ **Ya abrí Railway en tu navegador** → https://railway.app/new
- Si no se abrió, haz clic en el enlace de arriba

### 📋 Paso 2: Conectar GitHub
1. **Haz clic en "Deploy from GitHub repo"**
2. **Autoriza Railway** para acceder a tu GitHub (si es necesario)
3. **Busca tu repositorio:** `security_scanner_web`
4. **Selecciona:** `stoja88/security_scanner_web`

### 📋 Paso 3: Configuración Automática
Railway detectará automáticamente:
- ✅ **Dockerfile:** `deployments/railway/Dockerfile.railway`
- ✅ **Configuración:** `deployments/railway/railway.toml`
- ✅ **Variables de entorno** ya configuradas

### 📋 Paso 4: Deploy
1. **Haz clic en "Deploy"**
2. **Espera 5-10 minutos** mientras Railway construye tu aplicación
3. **¡Listo!** Railway te dará una URL como: `https://tu-app.railway.app`

## 🛠️ Configuración Incluida

### Herramientas de Seguridad
- ✅ **Nuclei v3.4.4** - Scanner de vulnerabilidades
- ✅ **theHarvester** - Recolección OSINT
- ✅ **Assetfinder** - Enumeración de subdominios
- ✅ **dig** - Consultas DNS
- ✅ **whois** - Información de dominios

### Variables de Entorno
```
FLASK_ENV=production
FLASK_DEBUG=false
MAX_CONCURRENT_SCANS=2
SCAN_TIMEOUT=300
PYTHONUNBUFFERED=1
```

### Características
- ✅ **SSL/HTTPS automático**
- ✅ **Escalado automático**
- ✅ **Monitoreo incluido**
- ✅ **Logs en tiempo real**

## 🧪 Verificar el Deploy

Una vez completado:
1. **Accede a la URL** que te proporcione Railway
2. **Prueba la interfaz** - debería cargar la página principal
3. **Haz un escaneo de prueba** con un dominio que poseas
4. **Verifica que se generen reportes**

## 🎯 URL de Ejemplo
Tu aplicación estará disponible en algo como:
`https://security-scanner-web-production.railway.app`

## 💡 Consejos
- **El primer deploy toma 5-10 minutos** (instalando herramientas)
- **Los siguientes deploys serán más rápidos** (2-3 minutos)
- **Railway te dará logs en tiempo real** del proceso
- **Puedes ver el progreso** en el dashboard de Railway

## 🚨 Si algo sale mal
1. **Revisa los logs** en Railway dashboard
2. **Verifica que el repositorio** `stoja88/security_scanner_web` esté seleccionado
3. **Asegúrate** de que Railway detectó el `railway.toml`
4. **Contacta conmigo** si necesitas ayuda

---

🎉 **¡Tu Security Scanner estará listo en unos minutos!**
