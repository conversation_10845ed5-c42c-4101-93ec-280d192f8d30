# Docker-specific requirements for Security Scanner Web Interface
# Includes additional dependencies for containerized environment

# Core Flask dependencies (from original requirements.txt)
Flask==2.3.3
Werkzeug==2.3.7
Jinja2==3.1.2
MarkupSafe==2.1.3
click==8.1.7
blinker==1.6.3
itsdangerous==2.1.2

# Additional dependencies for Docker environment
gunicorn==21.2.0          # Production WSGI server
requests==2.31.0          # HTTP library for health checks
psutil==5.9.6             # System monitoring
python-dotenv==1.0.0      # Environment variable management

# Security and monitoring
cryptography==41.0.7      # Cryptographic recipes
certifi==2023.11.17       # Certificate validation

# JSON and data processing
ujson==5.8.0              # Fast JSON processing
python-dateutil==2.8.2    # Date parsing utilities

# Logging and debugging
colorlog==6.8.0           # Colored logging output
structlog==23.2.0         # Structured logging

# Optional: Database support for future enhancements
SQLAlchemy==2.0.23        # Database ORM
alembic==1.13.1           # Database migrations

# Optional: Caching support
redis==5.0.1              # Redis client
flask-caching==2.1.0      # Flask caching extension
