version: '3.8'

services:
  security-scanner:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: security-scanner-web
    ports:
      - "3333:3333"
    volumes:
      # Mount reports directory for persistent storage
      - ./reports:/app/reports
      # Mount logs directory (optional)
      - ./logs:/app/logs
    environment:
      # Flask configuration
      - FLASK_ENV=production
      - FLASK_DEBUG=false
      - SCANNER_HOST=0.0.0.0
      - SCANNER_PORT=3333
      # Security scanner configuration
      - MAX_CONCURRENT_SCANS=3
      - SCAN_TIMEOUT=300
      # Nuclei configuration
      - NUCLEI_TEMPLATES_PATH=/root/nuclei-templates
      - NUCLEI_CONFIG_PATH=/app/.config/nuclei
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3333/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - scanner-network
    # Security settings
    security_opt:
      - no-new-privileges:true
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Optional: Add a reverse proxy for production use
  nginx:
    image: nginx:alpine
    container_name: security-scanner-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - security-scanner
    networks:
      - scanner-network
    profiles:
      - production
    restart: unless-stopped

networks:
  scanner-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  reports:
    driver: local
  logs:
    driver: local
