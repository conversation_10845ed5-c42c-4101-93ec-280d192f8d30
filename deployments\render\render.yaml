# Render deployment configuration
services:
  - type: web
    name: security-scanner-web
    env: docker
    dockerfilePath: ./deployments/render/Dockerfile.render
    plan: starter
    region: oregon
    branch: main
    healthCheckPath: /
    envVars:
      - key: FLASK_ENV
        value: production
      - key: FLASK_DEBUG
        value: false
      - key: MAX_CONCURRENT_SCANS
        value: 2
      - key: SCAN_TIMEOUT
        value: 300
      - key: PYTHONUNBUFFERED
        value: 1
      - key: SECRET_KEY
        generateValue: true
    scaling:
      minInstances: 1
      maxInstances: 3
      targetMemoryPercent: 80
      targetCPUPercent: 80
