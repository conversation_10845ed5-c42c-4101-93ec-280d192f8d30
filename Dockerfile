# Security Scanner Web Interface - Multi-platform Docker Image
# Supports both macOS and Windows development environments

# Use Ubuntu as base for better tool compatibility
FROM ubuntu:22.04 as base

# Set environment variables for non-interactive installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Install system dependencies and security tools
RUN apt-get update && apt-get install -y \
    # Python and pip
    python3 \
    python3-pip \
    python3-venv \
    # Network tools (dig, whois)
    dnsutils \
    whois \
    # Build tools for Go and other dependencies
    wget \
    curl \
    git \
    build-essential \
    # Additional utilities
    ca-certificates \
    software-properties-common \
    apt-transport-https \
    gnupg \
    lsb-release \
    unzip \
    # Clean up
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Go (required for assetfinder)
ENV GO_VERSION=1.21.5
RUN wget -q https://golang.org/dl/go${GO_VERSION}.linux-amd64.tar.gz \
    && tar -C /usr/local -xzf go${GO_VERSION}.linux-amd64.tar.gz \
    && rm go${GO_VERSION}.linux-amd64.tar.gz

# Set Go environment
ENV PATH="/usr/local/go/bin:${PATH}"
ENV GOPATH="/go"
ENV PATH="${GOPATH}/bin:${PATH}"

# Install Nuclei
RUN wget -q https://github.com/projectdiscovery/nuclei/releases/latest/download/nuclei_3.4.4_linux_amd64.zip \
    && unzip nuclei_3.4.4_linux_amd64.zip \
    && mv nuclei /usr/local/bin/ \
    && chmod +x /usr/local/bin/nuclei \
    && rm nuclei_3.4.4_linux_amd64.zip \
    && nuclei -update-templates

# Install theHarvester
RUN git clone https://github.com/laramies/theHarvester.git /opt/theHarvester \
    && cd /opt/theHarvester \
    && python3 -m pip install -r requirements.txt \
    && chmod +x theHarvester.py \
    && ln -s /opt/theHarvester/theHarvester.py /usr/local/bin/theharvester

# Install Assetfinder
RUN go install github.com/tomnomnom/assetfinder@latest \
    && cp ${GOPATH}/bin/assetfinder /usr/local/bin/

# Create application user and directories
RUN useradd -m -u 1000 scanner \
    && mkdir -p /app /app/reports /app/static /app/templates \
    && chown -R scanner:scanner /app

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt requirements-docker.txt ./

# Install Python dependencies
RUN python3 -m pip install --no-cache-dir --upgrade pip \
    && python3 -m pip install --no-cache-dir -r requirements.txt \
    && python3 -m pip install --no-cache-dir -r requirements-docker.txt

# Copy application files
COPY --chown=scanner:scanner . .

# Create docker-specific entrypoint
COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Switch to non-root user
USER scanner

# Expose port
EXPOSE 3333

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3333/ || exit 1

# Set entrypoint
ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
CMD ["python3", "app.py"]
