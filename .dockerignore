# Docker ignore file for Security Scanner Web Interface
# Excludes unnecessary files from Docker build context

# Version control
.git
.gitignore
.gitattributes

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Logs and temporary files
*.log
*.tmp
*.temp
app.log
logs/

# Reports (will be mounted as volume)
reports/*.html
reports/*.json
reports/*.xml

# Documentation (not needed in container)
*.md
docs/
ACTUALIZACIONES_COMPLETADAS.md
COMO_INICIAR.md
ESTADO_FINAL.md
FRONTEND_FIX.md
FRONTEND_IMPROVEMENTS.md
HERRAMIENTAS_REPARADAS.md
NUCLEI_AND_DARKMODE_FIXES.md
NUCLEI_MEJORADO.md
OPTIMIZACIONES_FINALES.md
PROYECTO_COMPLETADO.md

# Scripts (not needed in container)
start.sh
start_scanner.sh
fix_tools.py

# Test files
test_*.py
*_test.py
tests/

# Configuration examples
config_example.py

# Docker files (avoid recursion)
Dockerfile*
docker-compose*.yml
.dockerignore

# OS specific
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.bak
*.backup
*.old

# Temporary directories
tmp/
temp/
