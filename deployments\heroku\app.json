{"name": "Security Scanner Web Interface", "description": "A web-based security scanner with multiple tools for vulnerability assessment", "repository": "https://github.com/your-username/security_scanner_web", "logo": "https://raw.githubusercontent.com/your-username/security_scanner_web/main/static/logo.png", "keywords": ["security", "scanner", "vulnerability", "nuclei", "theharvester", "assetfinder", "web-security"], "stack": "container", "env": {"FLASK_ENV": {"description": "Flask environment", "value": "production"}, "FLASK_DEBUG": {"description": "Flask debug mode", "value": "false"}, "SECRET_KEY": {"description": "Secret key for Flask sessions", "generator": "secret"}, "MAX_CONCURRENT_SCANS": {"description": "Maximum number of concurrent scans", "value": "2"}, "SCAN_TIMEOUT": {"description": "Scan timeout in seconds", "value": "300"}}, "formation": {"web": {"quantity": 1, "size": "standard-1x"}}, "addons": [{"plan": "heroku-postgresql:mini", "as": "DATABASE"}], "buildpacks": [], "environments": {"test": {"scripts": {"test": "python -m pytest tests/"}}}}