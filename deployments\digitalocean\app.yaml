# DigitalOcean App Platform deployment configuration
name: security-scanner-web
region: nyc

services:
- name: web
  source_dir: /
  github:
    repo: your-username/security_scanner_web
    branch: main
  run_command: gunicorn --bind 0.0.0.0:$PORT --workers 2 --timeout 120 app:app
  environment_slug: ubuntu-22-04
  instance_count: 1
  instance_size_slug: basic-xxs
  dockerfile_path: deployments/digitalocean/Dockerfile.digitalocean
  
  envs:
  - key: FLASK_ENV
    value: production
  - key: FLASK_DEBUG
    value: "false"
  - key: MAX_CONCURRENT_SCANS
    value: "2"
  - key: SCAN_TIMEOUT
    value: "300"
  - key: PYTHONUNBUFFERED
    value: "1"
  - key: SECRET_KEY
    type: SECRET
    
  health_check:
    http_path: /
    initial_delay_seconds: 30
    period_seconds: 30
    timeout_seconds: 10
    success_threshold: 1
    failure_threshold: 3

  routes:
  - path: /
    preserve_path_prefix: true
