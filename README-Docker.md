# Security Scanner Web Interface - Docker Setup

This document provides comprehensive instructions for running the Security Scanner Web Interface using Docker, ensuring cross-platform compatibility between macOS and Windows.

## 🐳 Docker Overview

The Docker setup includes:
- **Multi-stage Dockerfile** with optimized layers
- **All security tools pre-installed** (nuclei, theharvester, assetfinder, dig, whois)
- **Cross-platform compatibility** (Windows, macOS, Linux)
- **Production-ready configuration** with nginx reverse proxy
- **Persistent volume mounts** for reports and logs
- **Health checks and monitoring**

## 📋 Prerequisites

### Required Software
- **Docker Desktop** (Windows/macOS) or **Docker Engine** (Linux)
- **Docker Compose** (included with Docker Desktop)
- **Git** (to clone the repository)

### System Requirements
- **RAM**: Minimum 2GB, Recommended 4GB
- **Storage**: 2GB free space for Docker images
- **Network**: Internet access for downloading tools and templates

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd security_scanner_web
```

### 2. Build and Run with Dock<PERSON> Compose
```bash
# Build and start the container
docker-compose up --build

# Or run in background
docker-compose up -d --build
```

### 3. Access the Application
- **Web Interface**: http://localhost:3333
- **Health Check**: http://localhost:3333/

### 4. Stop the Application
```bash
# Stop the container
docker-compose down

# Stop and remove volumes
docker-compose down -v
```

## 🔧 Configuration Options

### Environment Variables

You can customize the application by setting environment variables in `docker-compose.yml`:

```yaml
environment:
  - FLASK_ENV=production          # development/production
  - FLASK_DEBUG=false             # true/false
  - SCANNER_HOST=0.0.0.0         # Host to bind to
  - SCANNER_PORT=3333            # Port to listen on
  - MAX_CONCURRENT_SCANS=3       # Max simultaneous scans
  - SCAN_TIMEOUT=300             # Scan timeout in seconds
```

### Volume Mounts

The Docker setup includes persistent storage:

```yaml
volumes:
  - ./reports:/app/reports       # Scan reports
  - ./logs:/app/logs            # Application logs
```

### Port Configuration

To change the external port, modify `docker-compose.yml`:

```yaml
ports:
  - "8080:3333"  # External:Internal
```

## 🛠️ Advanced Usage

### Building the Image Manually

```bash
# Build the Docker image
docker build -t security-scanner:latest .

# Run the container
docker run -d \
  --name security-scanner \
  -p 3333:3333 \
  -v $(pwd)/reports:/app/reports \
  security-scanner:latest
```

### Development Mode

For development with live code reloading:

```bash
# Create development override
cat > docker-compose.override.yml << EOF
version: '3.8'
services:
  security-scanner:
    volumes:
      - .:/app
    environment:
      - FLASK_ENV=development
      - FLASK_DEBUG=true
    command: python3 app.py
EOF

# Start in development mode
docker-compose up
```

### Production Deployment

For production use with nginx reverse proxy:

```bash
# Start with nginx proxy
docker-compose --profile production up -d

# Access via nginx
# HTTP: http://localhost:80
# HTTPS: https://localhost:443 (requires SSL certificates)
```

## 🔍 Tool Verification

The container includes all required security tools:

```bash
# Check tool availability
docker exec security-scanner-web nuclei -version
docker exec security-scanner-web theharvester --help
docker exec security-scanner-web assetfinder --help
docker exec security-scanner-web dig -v
docker exec security-scanner-web whois --version
```

## 📊 Monitoring and Logs

### View Container Logs
```bash
# View real-time logs
docker-compose logs -f security-scanner

# View specific service logs
docker logs security-scanner-web
```

### Health Checks
```bash
# Check container health
docker ps
docker inspect security-scanner-web | grep Health

# Manual health check
curl http://localhost:3333/
```

### Resource Monitoring
```bash
# Monitor resource usage
docker stats security-scanner-web

# View container details
docker inspect security-scanner-web
```

## 🔒 Security Considerations

### Container Security
- Runs as **non-root user** (scanner:1000)
- **Security options** configured in docker-compose.yml
- **Resource limits** to prevent resource exhaustion
- **Network isolation** with custom bridge network

### Data Security
- Reports stored in **mounted volumes** (not in container)
- **No sensitive data** in Docker images
- **Environment variables** for configuration

### Network Security
- **Rate limiting** configured in nginx
- **Security headers** added by nginx
- **Internal network** for service communication

## 🐛 Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Check what's using the port
netstat -tulpn | grep 3333

# Use different port
sed -i 's/3333:3333/8080:3333/' docker-compose.yml
```

#### Permission Issues (Linux)
```bash
# Fix volume permissions
sudo chown -R 1000:1000 reports logs
```

#### Tool Not Working
```bash
# Check tool installation
docker exec security-scanner-web which nuclei
docker exec security-scanner-web nuclei -version

# Update nuclei templates
docker exec security-scanner-web nuclei -update-templates
```

#### Container Won't Start
```bash
# Check logs
docker-compose logs security-scanner

# Rebuild image
docker-compose build --no-cache security-scanner
```

### Debug Mode

Enable debug mode for troubleshooting:

```bash
# Set debug environment
export FLASK_DEBUG=true
export FLASK_ENV=development

# Rebuild and run
docker-compose up --build
```

## 📁 File Structure

```
security_scanner_web/
├── Dockerfile                 # Main container definition
├── docker-compose.yml        # Multi-service orchestration
├── docker-entrypoint.sh      # Container startup script
├── .dockerignore             # Files to exclude from build
├── nginx.conf                # Nginx reverse proxy config
├── requirements-docker.txt   # Docker-specific dependencies
├── README-Docker.md          # This file
└── app.py                    # Main application
```

## 🔄 Updates and Maintenance

### Updating the Application
```bash
# Pull latest changes
git pull origin main

# Rebuild and restart
docker-compose up --build -d
```

### Updating Security Tools
```bash
# Update nuclei templates
docker exec security-scanner-web nuclei -update-templates

# Rebuild image for tool updates
docker-compose build --no-cache security-scanner
```

### Cleanup
```bash
# Remove unused images
docker image prune

# Remove all stopped containers
docker container prune

# Full cleanup (careful!)
docker system prune -a
```

## 🌐 Cross-Platform Notes

### Windows Specific
- Use **PowerShell** or **Command Prompt**
- **Docker Desktop** must be running
- **WSL2** backend recommended for better performance
- **File paths** use forward slashes in Docker commands

### macOS Specific
- **Docker Desktop** handles most configuration automatically
- **File sharing** may need to be enabled in Docker preferences
- **Performance** is generally better than Windows

### Linux Specific
- Install **Docker Engine** and **Docker Compose** separately
- User must be in **docker group**: `sudo usermod -aG docker $USER`
- **SELinux** may require additional configuration

## 🧪 Testing the Docker Setup

### Automated Setup Scripts

Use the provided setup scripts for your platform:

#### Windows (PowerShell)
```powershell
# Check Docker and build image
.\docker-setup.ps1 -Build

# Run in production mode
.\docker-setup.ps1 -Run

# Run in development mode
.\docker-setup.ps1 -Dev
```

#### macOS/Linux (Bash)
```bash
# Make script executable
chmod +x docker-setup.sh

# Check Docker and build image
./docker-setup.sh build

# Run in production mode
./docker-setup.sh run

# Run in development mode
./docker-setup.sh dev
```

### Manual Testing Steps

1. **Verify Docker Installation**
   ```bash
   docker --version
   docker-compose --version
   ```

2. **Build the Image**
   ```bash
   docker-compose build --no-cache
   ```

3. **Start the Application**
   ```bash
   docker-compose up -d
   ```

4. **Test the Web Interface**
   - Open browser to http://localhost:3333
   - Enter a test domain (e.g., `example.com`)
   - Start a security scan
   - Verify tools are working

5. **Check Container Health**
   ```bash
   docker ps
   curl http://localhost:3333/
   ```

6. **Verify Security Tools**
   ```bash
   docker exec security-scanner-web nuclei -version
   docker exec security-scanner-web theharvester --help
   docker exec security-scanner-web assetfinder --help
   ```

### Expected Results

After successful setup, you should see:
- ✅ All security tools installed and working
- ✅ Web interface accessible at http://localhost:3333
- ✅ Scans complete successfully with results
- ✅ Reports generated and downloadable
- ✅ Container health checks passing

---

🎯 **The Docker setup provides a consistent, isolated environment for the Security Scanner Web Interface across all platforms.**
