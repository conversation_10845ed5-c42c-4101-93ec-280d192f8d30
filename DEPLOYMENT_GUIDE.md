# Security Scanner Web Interface - Cloud Deployment Guide

This guide provides step-by-step instructions for deploying the Security Scanner Web Interface to various cloud platforms.

## 🌐 Supported Platforms

| Platform | Difficulty | Cost | Features | Best For |
|----------|------------|------|----------|----------|
| **Railway** | ⭐ Easy | $ Low | Auto-deploy, Docker | Quick deployment |
| **Render** | ⭐ Easy | $ Low | Auto-deploy, SSL | Simple hosting |
| **Heroku** | ⭐⭐ Medium | $$ Medium | Add-ons, Scaling | Production apps |
| **Fly.io** | ⭐⭐ Medium | $ Low | Global edge | Performance |
| **DigitalOcean** | ⭐⭐ Medium | $$ Medium | App Platform | Balanced option |
| **AWS** | ⭐⭐⭐ Hard | $$$ High | Full control | Enterprise |
| **Vercel** | ⭐⭐⭐ Hard | $ Low | Serverless | Limited functionality* |

*Note: Vercel has limitations for security tools due to serverless constraints.

## 🚀 Quick Deployment Options

### 1. Railway (Recommended for Beginners)

**Why Railway?**
- Easiest deployment process
- Automatic Docker builds
- Free tier available
- Great for testing

**Steps:**
1. **Sign up** at [railway.app](https://railway.app)
2. **Connect GitHub** repository
3. **Deploy from GitHub:**
   ```bash
   # Push your code to GitHub first
   git add .
   git commit -m "Add Railway deployment"
   git push origin main
   ```
4. **Configure in Railway:**
   - Select your repository
   - Railway auto-detects the `railway.toml` file
   - Set environment variables if needed
5. **Access your app** at the provided Railway URL

**Environment Variables:**
```
FLASK_ENV=production
FLASK_DEBUG=false
MAX_CONCURRENT_SCANS=2
SCAN_TIMEOUT=300
```

### 2. Render (Great for Production)

**Why Render?**
- Free SSL certificates
- Automatic deployments
- Good performance
- Reasonable pricing

**Steps:**
1. **Sign up** at [render.com](https://render.com)
2. **Connect GitHub** repository
3. **Create Web Service:**
   - Choose "Docker" as environment
   - Set Dockerfile path: `deployments/render/Dockerfile.render`
   - Configure auto-deploy from main branch
4. **Set Environment Variables** in Render dashboard
5. **Deploy** and access your app

### 3. Heroku (Traditional Choice)

**Why Heroku?**
- Mature platform
- Extensive add-on ecosystem
- Good documentation
- Reliable

**Steps:**
1. **Install Heroku CLI:**
   ```bash
   # Windows
   winget install Heroku.CLI
   
   # macOS
   brew tap heroku/brew && brew install heroku
   ```

2. **Login and create app:**
   ```bash
   heroku login
   heroku create your-security-scanner
   ```

3. **Set stack to container:**
   ```bash
   heroku stack:set container -a your-security-scanner
   ```

4. **Deploy:**
   ```bash
   git add .
   git commit -m "Add Heroku deployment"
   git push heroku main
   ```

5. **Set environment variables:**
   ```bash
   heroku config:set FLASK_ENV=production -a your-security-scanner
   heroku config:set MAX_CONCURRENT_SCANS=2 -a your-security-scanner
   ```

## 🔧 Platform-Specific Configurations

### Railway Configuration

File: `deployments/railway/railway.toml`
- Uses Docker build
- Automatic port detection
- Health checks enabled
- Restart policy configured

### Render Configuration

File: `deployments/render/render.yaml`
- Auto-scaling enabled
- Health checks configured
- SSL termination
- Custom domains supported

### Heroku Configuration

Files: 
- `deployments/heroku/heroku.yml`
- `deployments/heroku/app.json`
- `deployments/heroku/Dockerfile.heroku`

Features:
- Container stack
- Add-ons support (PostgreSQL)
- Dyno scaling
- Review apps

### Fly.io Configuration

File: `deployments/fly/fly.toml`
- Global edge deployment
- Volume mounts for persistence
- Auto-stop/start machines
- Health checks

### DigitalOcean Configuration

File: `deployments/digitalocean/app.yaml`
- App Platform deployment
- Auto-scaling
- Health monitoring
- Custom domains

### AWS Configuration

Files:
- `deployments/aws/Dockerfile.aws`
- `deployments/aws/apprunner.yaml`

Services:
- AWS App Runner (easiest)
- ECS Fargate (advanced)
- Elastic Beanstalk (traditional)

## ⚠️ Important Security Considerations

### 🔒 Production Security

**Before deploying to production:**

1. **Change Secret Keys:**
   ```bash
   # Generate a secure secret key
   python3 -c "import secrets; print(secrets.token_hex(32))"
   ```

2. **Set Environment Variables:**
   ```
   SECRET_KEY=your-generated-secret-key
   FLASK_ENV=production
   FLASK_DEBUG=false
   ```

3. **Configure Rate Limiting:**
   ```
   MAX_CONCURRENT_SCANS=2
   SCAN_TIMEOUT=300
   ```

4. **Enable HTTPS:**
   - Most platforms provide free SSL
   - Force HTTPS redirects
   - Set secure headers

### 🛡️ Security Tools Limitations

**Important Notes:**
- Some platforms may restrict certain security tools
- Nuclei templates require internet access
- Scanning external domains may trigger security policies
- Consider legal implications of security scanning

### 📝 Legal Compliance

**Before deployment:**
- Only scan domains you own or have permission to test
- Add terms of service to your application
- Implement user authentication if needed
- Consider data privacy regulations

## 🔍 Testing Your Deployment

### Basic Health Check
```bash
# Test if the application is running
curl https://your-app-url.com/

# Test a simple scan (use your own domain)
curl -X POST https://your-app-url.com/scan \
  -H "Content-Type: application/json" \
  -d '{"target": "your-domain.com"}'
```

### Verify Security Tools
```bash
# Check if tools are available (if you have shell access)
nuclei -version
theharvester --help
assetfinder --help
dig -v
whois --version
```

## 💰 Cost Estimates

### Free Tiers
- **Railway:** 500 hours/month free
- **Render:** 750 hours/month free
- **Heroku:** 1000 dyno hours/month free (with credit card)
- **Fly.io:** $5 credit monthly
- **Vercel:** Generous free tier

### Paid Plans (Monthly)
- **Railway:** $5-20/month
- **Render:** $7-25/month
- **Heroku:** $7-25/month
- **DigitalOcean:** $5-12/month
- **AWS:** $10-50/month (varies greatly)

## 🚨 Troubleshooting

### Common Issues

1. **Build Timeouts:**
   - Reduce Docker image size
   - Use multi-stage builds
   - Pre-build security tools

2. **Memory Limits:**
   - Reduce concurrent scans
   - Optimize tool configurations
   - Upgrade to higher tier

3. **Tool Installation Failures:**
   - Check internet connectivity during build
   - Use alternative installation methods
   - Pre-download tools

4. **Port Binding Issues:**
   - Use environment variable `$PORT`
   - Bind to `0.0.0.0` not `localhost`
   - Check platform-specific port requirements

### Getting Help

- Check platform-specific logs
- Review deployment configurations
- Test locally with Docker first
- Contact platform support if needed

## 📚 Next Steps

After successful deployment:

1. **Set up monitoring** (logs, metrics)
2. **Configure custom domain** (if needed)
3. **Implement authentication** (for production use)
4. **Set up backup strategy** (for reports)
5. **Monitor resource usage** and costs

---

🎯 **Choose the platform that best fits your needs, budget, and technical expertise. Railway and Render are excellent starting points for most users.**
