# Railway-optimized Dockerfile for Security Scanner Web Interface
# Simplified version to avoid package conflicts

FROM python:3.11-slim

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    dnsutils \
    whois \
    wget \
    curl \
    git \
    build-essential \
    ca-certificates \
    unzip \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Go
ENV GO_VERSION=1.21.5
RUN wget -q https://golang.org/dl/go${GO_VERSION}.linux-amd64.tar.gz \
    && tar -C /usr/local -xzf go${GO_VERSION}.linux-amd64.tar.gz \
    && rm go${GO_VERSION}.linux-amd64.tar.gz

ENV PATH="/usr/local/go/bin:${PATH}"
ENV GOPATH="/go"
ENV PATH="${GOPATH}/bin:${PATH}"

# Install Nuclei
RUN wget -q https://github.com/projectdiscovery/nuclei/releases/latest/download/nuclei_3.4.4_linux_amd64.zip \
    && unzip nuclei_3.4.4_linux_amd64.zip \
    && mv nuclei /usr/local/bin/ \
    && chmod +x /usr/local/bin/nuclei \
    && rm nuclei_3.4.4_linux_amd64.zip

# Install theHarvester
RUN git clone https://github.com/laramies/theHarvester.git /opt/theHarvester \
    && cd /opt/theHarvester \
    && pip install -r requirements.txt \
    && chmod +x theHarvester.py \
    && ln -s /opt/theHarvester/theHarvester.py /usr/local/bin/theharvester

# Install Assetfinder
RUN go install github.com/tomnomnom/assetfinder@latest \
    && cp ${GOPATH}/bin/assetfinder /usr/local/bin/

# Create app directory
WORKDIR /app

# Install Python dependencies directly
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir Flask==2.3.3 \
    && pip install --no-cache-dir Werkzeug==2.3.7 \
    && pip install --no-cache-dir Jinja2==3.1.2 \
    && pip install --no-cache-dir MarkupSafe==2.1.3 \
    && pip install --no-cache-dir click==8.1.7 \
    && pip install --no-cache-dir blinker==1.6.3 \
    && pip install --no-cache-dir itsdangerous==2.1.2 \
    && pip install --no-cache-dir gunicorn==21.2.0 \
    && pip install --no-cache-dir requests==2.31.0

# Copy application files
COPY . .

# Create reports directory
RUN mkdir -p reports

# Update nuclei templates
RUN timeout 60 nuclei -update-templates -silent || true

# Railway uses PORT environment variable
EXPOSE $PORT

CMD gunicorn --bind 0.0.0.0:$PORT --workers 2 --timeout 120 app:app
