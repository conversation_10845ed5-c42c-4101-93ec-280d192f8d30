# Security Scanner Web Interface - PowerShell Deployment Script
# Supports multiple cloud platforms on Windows

param(
    [Parameter(Position=0)]
    [ValidateSet("railway", "render", "heroku", "fly", "docker", "help")]
    [string]$Platform = "help",
    
    [switch]$Test,
    [switch]$Force,
    [switch]$Help
)

# Colors for output
$Red = "`e[31m"
$Green = "`e[32m"
$Yellow = "`e[33m"
$Blue = "`e[34m"
$Reset = "`e[0m"

function Write-ColorOutput {
    param($Color, $Message)
    Write-Host "$Color$Message$Reset"
}

function Show-Help {
    Write-ColorOutput $Blue "Security Scanner Web Interface - Deployment Script"
    Write-Host "=================================================="
    Write-Host ""
    Write-Host "Usage: .\deploy.ps1 [PLATFORM] [OPTIONS]"
    Write-Host ""
    Write-Host "Platforms:"
    Write-Host "  railway     Deploy to Railway (recommended for beginners)"
    Write-Host "  render      Deploy to Render"
    Write-Host "  heroku      Deploy to Heroku"
    Write-Host "  fly         Deploy to Fly.io"
    Write-Host "  docker      Build and test Docker image locally"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Test       Test deployment locally first"
    Write-Host "  -Force      Force deployment without confirmation"
    Write-Host "  -Help       Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\deploy.ps1 railway"
    Write-Host "  .\deploy.ps1 heroku -Test"
    Write-Host "  .\deploy.ps1 docker"
    Write-Host ""
}

function Test-Prerequisites {
    Write-ColorOutput $Blue "🔍 Checking prerequisites..."
    
    # Check if git is installed
    try {
        git --version | Out-Null
        Write-ColorOutput $Green "✅ Git is available"
    } catch {
        Write-ColorOutput $Red "❌ Git is not installed"
        Write-Host "Please install Git from: https://git-scm.com/download/win"
        exit 1
    }
    
    # Check if we're in a git repository
    try {
        git rev-parse --git-dir | Out-Null
        Write-ColorOutput $Green "✅ Git repository detected"
    } catch {
        Write-ColorOutput $Red "❌ Not in a git repository"
        Write-Host "Please initialize git repository first:"
        Write-Host "  git init"
        Write-Host "  git add ."
        Write-Host "  git commit -m 'Initial commit'"
        exit 1
    }
    
    Write-ColorOutput $Green "✅ Prerequisites check passed"
}

function Test-DockerBuild {
    Write-ColorOutput $Blue "🔨 Testing Docker build locally..."
    
    # Check if Docker is installed
    try {
        docker --version | Out-Null
        Write-ColorOutput $Green "✅ Docker is available"
    } catch {
        Write-ColorOutput $Red "❌ Docker is not installed"
        Write-Host "Please install Docker Desktop from: https://www.docker.com/products/docker-desktop"
        exit 1
    }
    
    # Check if Docker is running
    try {
        docker info | Out-Null
        Write-ColorOutput $Green "✅ Docker is running"
    } catch {
        Write-ColorOutput $Red "❌ Docker is not running"
        Write-Host "Please start Docker Desktop"
        exit 1
    }
    
    # Build the image
    Write-ColorOutput $Blue "🔨 Building Docker image..."
    docker build -t security-scanner:test .
    
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput $Green "✅ Docker build successful"
        
        # Test run
        Write-ColorOutput $Blue "🧪 Testing container..."
        docker run -d --name security-scanner-test -p 3333:3333 security-scanner:test
        
        # Wait for startup
        Start-Sleep -Seconds 10
        
        # Test health check
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:3333/" -TimeoutSec 10
            if ($response.StatusCode -eq 200) {
                Write-ColorOutput $Green "✅ Container health check passed"
            }
        } catch {
            Write-ColorOutput $Yellow "⚠️  Container health check failed (may be normal during startup)"
        }
        
        # Cleanup
        docker stop security-scanner-test | Out-Null
        docker rm security-scanner-test | Out-Null
        
        Write-ColorOutput $Green "✅ Local Docker test completed"
    } else {
        Write-ColorOutput $Red "❌ Docker build failed"
        exit 1
    }
}

function Deploy-Railway {
    Write-ColorOutput $Blue "🚂 Deploying to Railway..."
    
    # Check if railway CLI is installed
    try {
        railway --version | Out-Null
        Write-ColorOutput $Green "✅ Railway CLI is available"
    } catch {
        Write-ColorOutput $Yellow "⚠️  Railway CLI not found. Please install it manually:"
        Write-Host "1. Go to https://railway.app/cli"
        Write-Host "2. Download and install the CLI"
        Write-Host "3. Run this script again"
        exit 1
    }
    
    # Login to Railway
    Write-ColorOutput $Blue "🔐 Please login to Railway..."
    railway login
    
    if ($LASTEXITCODE -eq 0) {
        # Initialize project
        railway init
        
        # Deploy
        Write-ColorOutput $Blue "🚀 Deploying to Railway..."
        railway up
        
        Write-ColorOutput $Green "✅ Deployment to Railway completed!"
        Write-ColorOutput $Blue "🌐 Your app will be available at the Railway-provided URL"
    } else {
        Write-ColorOutput $Red "❌ Railway login failed"
        exit 1
    }
}

function Deploy-Render {
    Write-ColorOutput $Blue "🎨 Deploying to Render..."
    
    Write-ColorOutput $Yellow "📝 Manual steps required for Render deployment:"
    Write-Host ""
    Write-Host "1. Go to https://render.com and sign up/login"
    Write-Host "2. Connect your GitHub repository"
    Write-Host "3. Create a new Web Service"
    Write-Host "4. Select your repository"
    Write-Host "5. Set the following configuration:"
    Write-Host "   - Environment: Docker"
    Write-Host "   - Dockerfile Path: deployments/render/Dockerfile.render"
    Write-Host "   - Build Command: (leave empty)"
    Write-Host "   - Start Command: (leave empty)"
    Write-Host "6. Add environment variables:"
    Write-Host "   - FLASK_ENV=production"
    Write-Host "   - FLASK_DEBUG=false"
    Write-Host "   - MAX_CONCURRENT_SCANS=2"
    Write-Host "7. Deploy!"
    Write-Host ""
    Write-ColorOutput $Green "✅ Render deployment configuration ready"
}

function Deploy-Heroku {
    Write-ColorOutput $Blue "🟣 Deploying to Heroku..."
    
    # Check if heroku CLI is installed
    try {
        heroku --version | Out-Null
        Write-ColorOutput $Green "✅ Heroku CLI is available"
    } catch {
        Write-ColorOutput $Red "❌ Heroku CLI is not installed"
        Write-Host "Please install Heroku CLI:"
        Write-Host "  winget install Heroku.CLI"
        Write-Host "Or download from: https://devcenter.heroku.com/articles/heroku-cli"
        exit 1
    }
    
    # Login to Heroku
    Write-ColorOutput $Blue "🔐 Please login to Heroku..."
    heroku login
    
    if ($LASTEXITCODE -eq 0) {
        # Create app
        $appName = Read-Host "Enter your Heroku app name (or press Enter for auto-generated)"
        
        if ([string]::IsNullOrWhiteSpace($appName)) {
            heroku create
        } else {
            heroku create $appName
        }
        
        # Set stack to container
        heroku stack:set container
        
        # Set environment variables
        Write-ColorOutput $Blue "⚙️  Setting environment variables..."
        heroku config:set FLASK_ENV=production
        heroku config:set FLASK_DEBUG=false
        heroku config:set MAX_CONCURRENT_SCANS=2
        heroku config:set SCAN_TIMEOUT=300
        
        # Deploy
        Write-ColorOutput $Blue "🚀 Deploying to Heroku..."
        git push heroku main
        
        # Open app
        heroku open
        
        Write-ColorOutput $Green "✅ Deployment to Heroku completed!"
    } else {
        Write-ColorOutput $Red "❌ Heroku login failed"
        exit 1
    }
}

function Deploy-Fly {
    Write-ColorOutput $Blue "🪰 Deploying to Fly.io..."
    
    # Check if flyctl is installed
    try {
        flyctl version | Out-Null
        Write-ColorOutput $Green "✅ Fly CLI is available"
    } catch {
        Write-ColorOutput $Yellow "⚠️  Fly CLI not found. Please install it manually:"
        Write-Host "1. Go to https://fly.io/docs/hands-on/install-flyctl/"
        Write-Host "2. Download and install flyctl for Windows"
        Write-Host "3. Run this script again"
        exit 1
    }
    
    # Login to Fly
    Write-ColorOutput $Blue "🔐 Please login to Fly.io..."
    flyctl auth login
    
    if ($LASTEXITCODE -eq 0) {
        # Launch app
        Write-ColorOutput $Blue "🚀 Launching app on Fly.io..."
        flyctl launch --config deployments/fly/fly.toml
        
        Write-ColorOutput $Green "✅ Deployment to Fly.io completed!"
    } else {
        Write-ColorOutput $Red "❌ Fly.io login failed"
        exit 1
    }
}

# Main execution
if ($Help) {
    Show-Help
    exit 0
}

switch ($Platform) {
    "railway" {
        Test-Prerequisites
        if ($Test) { Test-DockerBuild }
        Deploy-Railway
    }
    "render" {
        Test-Prerequisites
        if ($Test) { Test-DockerBuild }
        Deploy-Render
    }
    "heroku" {
        Test-Prerequisites
        if ($Test) { Test-DockerBuild }
        Deploy-Heroku
    }
    "fly" {
        Test-Prerequisites
        if ($Test) { Test-DockerBuild }
        Deploy-Fly
    }
    "docker" {
        Test-DockerBuild
    }
    default {
        Show-Help
    }
}
