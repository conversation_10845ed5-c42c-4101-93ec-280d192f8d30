# Security Scanner Web Interface - Docker Makefile
# Simplifies common Docker operations for cross-platform development

.PHONY: help build run dev stop clean logs shell test health

# Default target
help:
	@echo "Security Scanner Web Interface - Docker Commands"
	@echo "================================================"
	@echo ""
	@echo "Available commands:"
	@echo "  build     - Build the Docker image"
	@echo "  run       - Run the application in production mode"
	@echo "  dev       - Run the application in development mode"
	@echo "  stop      - Stop all containers"
	@echo "  clean     - Stop containers and remove images"
	@echo "  logs      - View application logs"
	@echo "  shell     - Open shell in running container"
	@echo "  test      - Run tests in container"
	@echo "  health    - Check application health"
	@echo "  tools     - Verify security tools in container"
	@echo ""
	@echo "Examples:"
	@echo "  make build && make run"
	@echo "  make dev"
	@echo "  make logs"

# Build the Docker image
build:
	@echo "🔨 Building Security Scanner Docker image..."
	docker-compose build --no-cache

# Run in production mode
run:
	@echo "🚀 Starting Security Scanner in production mode..."
	docker-compose up -d
	@echo "✅ Application started at http://localhost:3333"

# Run in development mode with hot reloading
dev:
	@echo "🔧 Starting Security Scanner in development mode..."
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
	@echo "✅ Development server started at http://localhost:3333"

# Stop all containers
stop:
	@echo "⏹️  Stopping Security Scanner containers..."
	docker-compose down

# Clean up containers and images
clean:
	@echo "🧹 Cleaning up Docker resources..."
	docker-compose down -v --rmi all
	docker system prune -f

# View logs
logs:
	@echo "📋 Viewing Security Scanner logs..."
	docker-compose logs -f security-scanner

# Open shell in running container
shell:
	@echo "🐚 Opening shell in Security Scanner container..."
	docker exec -it security-scanner-web /bin/bash

# Run tests in container
test:
	@echo "🧪 Running tests in container..."
	docker exec security-scanner-web python3 -m pytest tests/ -v

# Check application health
health:
	@echo "🏥 Checking application health..."
	@curl -f http://localhost:3333/ > /dev/null 2>&1 && echo "✅ Application is healthy" || echo "❌ Application is not responding"

# Verify security tools
tools:
	@echo "🔍 Verifying security tools in container..."
	@echo "Nuclei:"
	@docker exec security-scanner-web nuclei -version 2>/dev/null || echo "❌ Nuclei not available"
	@echo "theHarvester:"
	@docker exec security-scanner-web theharvester --help > /dev/null 2>&1 && echo "✅ theHarvester available" || echo "❌ theHarvester not available"
	@echo "Assetfinder:"
	@docker exec security-scanner-web assetfinder --help > /dev/null 2>&1 && echo "✅ Assetfinder available" || echo "❌ Assetfinder not available"
	@echo "dig:"
	@docker exec security-scanner-web dig -v > /dev/null 2>&1 && echo "✅ dig available" || echo "❌ dig not available"
	@echo "whois:"
	@docker exec security-scanner-web whois --version > /dev/null 2>&1 && echo "✅ whois available" || echo "❌ whois not available"

# Quick start (build and run)
start: build run

# Development quick start
start-dev: build dev

# Production deployment with nginx
production:
	@echo "🌐 Starting Security Scanner with nginx reverse proxy..."
	docker-compose --profile production up -d
	@echo "✅ Production deployment started"
	@echo "   HTTP:  http://localhost:80"
	@echo "   HTTPS: https://localhost:443 (requires SSL setup)"

# Update nuclei templates
update-nuclei:
	@echo "🔄 Updating Nuclei templates..."
	docker exec security-scanner-web nuclei -update-templates

# Backup reports
backup:
	@echo "💾 Creating backup of reports..."
	@mkdir -p backups
	@tar -czf backups/reports-$(shell date +%Y%m%d-%H%M%S).tar.gz reports/
	@echo "✅ Backup created in backups/ directory"

# Restore reports from backup
restore:
	@echo "📥 Available backups:"
	@ls -la backups/*.tar.gz 2>/dev/null || echo "No backups found"
	@echo "To restore: tar -xzf backups/reports-YYYYMMDD-HHMMSS.tar.gz"

# Show container status
status:
	@echo "📊 Container Status:"
	@docker-compose ps
	@echo ""
	@echo "📈 Resource Usage:"
	@docker stats --no-stream security-scanner-web 2>/dev/null || echo "Container not running"
