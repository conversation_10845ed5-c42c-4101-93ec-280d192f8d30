#!/bin/bash
# Security Scanner Web Interface - Docker Setup Script for macOS/Linux
# Bash script to set up and run the Docker environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to show help
show_help() {
    print_color $BLUE "Security Scanner Web Interface - Docker Setup"
    echo "=============================================="
    echo ""
    echo "Usage: ./docker-setup.sh [OPTION]"
    echo ""
    echo "Options:"
    echo "  build     Build the Docker image"
    echo "  run       Run the application in production mode"
    echo "  dev       Run the application in development mode"
    echo "  stop      Stop all containers"
    echo "  clean     Clean up containers and images"
    echo "  logs      View application logs"
    echo "  shell     Open shell in running container"
    echo "  health    Check application health"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  ./docker-setup.sh build"
    echo "  ./docker-setup.sh run"
    echo "  ./docker-setup.sh dev"
    echo ""
}

# Function to test Docker installation
test_docker_installation() {
    print_color $BLUE "🔍 Checking Docker installation..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        print_color $RED "❌ Docker is not installed"
        echo ""
        echo "Please install Docker:"
        echo "macOS: Download Docker Desktop from https://www.docker.com/products/docker-desktop"
        echo "Linux: Follow instructions at https://docs.docker.com/engine/install/"
        exit 1
    fi
    
    print_color $GREEN "✅ Docker is installed: $(docker --version)"
    
    # Check if Docker is running
    if ! docker info &> /dev/null; then
        print_color $RED "❌ Docker is not running"
        echo ""
        echo "Please start Docker:"
        echo "macOS: Open Docker Desktop application"
        echo "Linux: sudo systemctl start docker"
        exit 1
    fi
    
    print_color $GREEN "✅ Docker is running"
    
    # Check Docker Compose
    if command -v docker-compose &> /dev/null; then
        print_color $GREEN "✅ Docker Compose is available: $(docker-compose --version)"
    elif docker compose version &> /dev/null; then
        print_color $GREEN "✅ Docker Compose (v2) is available"
        # Create alias for compatibility
        alias docker-compose='docker compose'
    else
        print_color $RED "❌ Docker Compose is not available"
        echo "Please install Docker Compose or update Docker Desktop"
        exit 1
    fi
}

# Function to build Docker image
build_image() {
    print_color $BLUE "🔨 Building Security Scanner Docker image..."
    
    # Check if Dockerfile exists
    if [ ! -f "Dockerfile" ]; then
        print_color $RED "❌ Dockerfile not found in current directory"
        echo "Please run this script from the security_scanner_web directory"
        exit 1
    fi
    
    # Build the image
    docker-compose build --no-cache
    print_color $GREEN "✅ Docker image built successfully"
}

# Function to start application
start_application() {
    local dev_mode=$1
    
    if [ "$dev_mode" = "true" ]; then
        print_color $BLUE "🔧 Starting Security Scanner in development mode..."
        docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
    else
        print_color $BLUE "🚀 Starting Security Scanner in production mode..."
        docker-compose up -d
        print_color $GREEN "✅ Application started successfully"
        echo ""
        echo "🌐 Access the application at: http://localhost:3333"
        echo "📊 View logs with: docker-compose logs -f"
        echo "⏹️  Stop with: docker-compose down"
    fi
}

# Function to stop application
stop_application() {
    print_color $BLUE "⏹️  Stopping Security Scanner containers..."
    docker-compose down
    print_color $GREEN "✅ Containers stopped successfully"
}

# Function to clean environment
clean_environment() {
    print_color $BLUE "🧹 Cleaning up Docker environment..."
    docker-compose down -v --rmi all
    docker system prune -f
    print_color $GREEN "✅ Environment cleaned successfully"
}

# Function to view logs
view_logs() {
    print_color $BLUE "📋 Viewing Security Scanner logs..."
    docker-compose logs -f security-scanner
}

# Function to open shell
open_shell() {
    print_color $BLUE "🐚 Opening shell in Security Scanner container..."
    docker exec -it security-scanner-web /bin/bash
}

# Function to check health
check_health() {
    print_color $BLUE "🏥 Checking application health..."
    if curl -f http://localhost:3333/ > /dev/null 2>&1; then
        print_color $GREEN "✅ Application is healthy"
    else
        print_color $RED "❌ Application is not responding"
        echo "Try: docker-compose logs security-scanner"
    fi
}

# Function to verify tools
verify_tools() {
    print_color $BLUE "🔍 Verifying security tools in container..."
    
    echo "Nuclei:"
    if docker exec security-scanner-web nuclei -version 2>/dev/null; then
        print_color $GREEN "✅ Nuclei available"
    else
        print_color $RED "❌ Nuclei not available"
    fi
    
    echo "theHarvester:"
    if docker exec security-scanner-web theharvester --help > /dev/null 2>&1; then
        print_color $GREEN "✅ theHarvester available"
    else
        print_color $RED "❌ theHarvester not available"
    fi
    
    echo "Assetfinder:"
    if docker exec security-scanner-web assetfinder --help > /dev/null 2>&1; then
        print_color $GREEN "✅ Assetfinder available"
    else
        print_color $RED "❌ Assetfinder not available"
    fi
    
    echo "dig:"
    if docker exec security-scanner-web dig -v > /dev/null 2>&1; then
        print_color $GREEN "✅ dig available"
    else
        print_color $RED "❌ dig not available"
    fi
    
    echo "whois:"
    if docker exec security-scanner-web whois --version > /dev/null 2>&1; then
        print_color $GREEN "✅ whois available"
    else
        print_color $RED "❌ whois not available"
    fi
}

# Main execution
case "${1:-help}" in
    "build")
        test_docker_installation
        build_image
        ;;
    "run")
        test_docker_installation
        start_application false
        ;;
    "dev")
        test_docker_installation
        start_application true
        ;;
    "stop")
        test_docker_installation
        stop_application
        ;;
    "clean")
        test_docker_installation
        clean_environment
        ;;
    "logs")
        view_logs
        ;;
    "shell")
        open_shell
        ;;
    "health")
        check_health
        ;;
    "tools")
        verify_tools
        ;;
    "help"|*)
        show_help
        ;;
esac
