# Security Scanner Web Interface - Versión de Desarrollo Local
# Esta versión simula las herramientas para testing sin dependencias externas

import os
import json
import uuid
import subprocess
import threading
from datetime import datetime
from flask import Flask, render_template, request, jsonify, send_file
import time
import re

app = Flask(__name__)

# Configuración
REPORTS_DIR = 'reports'
MAX_CONCURRENT_SCANS = 3
active_scans = {}
scan_results = {}

# Asegurar que el directorio de reportes existe
os.makedirs(REPORTS_DIR, exist_ok=True)

def save_scan_results(scan_id, results):
    """Guarda los resultados del scan en un archivo JSON"""
    try:
        results_file = os.path.join(REPORTS_DIR, f"scan_{scan_id}.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"Resultados guardados: {results_file}")
    except Exception as e:
        print(f"Error guardando resultados: {e}")

def load_scan_results(scan_id):
    """Carga los resultados del scan desde un archivo JSON"""
    try:
        results_file = os.path.join(REPORTS_DIR, f"scan_{scan_id}.json")
        if os.path.exists(results_file):
            with open(results_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return None
    except Exception as e:
        print(f"Error cargando resultados: {e}")
        return None

class SecurityScannerDev:
    def __init__(self, target, scan_id):
        self.target = target
        self.scan_id = scan_id
        self.results = {
            'scan_id': scan_id,
            'target': target,
            'status': 'running',
            'start_time': datetime.now().isoformat(),
            'end_time': None,
            'tools_results': {},
            'summary': {},
            'recommendations': []
        }

    def simulate_tool_delay(self, tool_name, min_seconds=2, max_seconds=5):
        """Simula el tiempo de ejecución de una herramienta"""
        import random
        delay = random.uniform(min_seconds, max_seconds)
        print(f"[{tool_name}] Simulando ejecución por {delay:.1f} segundos...")
        time.sleep(delay)

    def run_whois_dev(self):
        """Simula ejecución de whois"""
        self.results['tools_results']['whois'] = {
            'status': 'running',
            'progress': 'Consultando información de registro...'
        }
        
        self.simulate_tool_delay('whois', 1, 3)
        
        # Datos simulados
        whois_data = {
            'domain': self.target,
            'registrar': 'Example Registrar Inc.',
            'creation_date': '2020-01-15',
            'expiration_date': '2025-01-15',
            'name_servers': ['ns1.example.com', 'ns2.example.com'],
            'status': 'Active'
        }
        
        self.results['tools_results']['whois'] = {
            'status': 'completed',
            'progress': 'Información de registro obtenida',
            'output': whois_data,
            'raw_output': f"Domain: {self.target}\nRegistrar: Example Registrar Inc.\nCreated: 2020-01-15\nExpires: 2025-01-15"
        }

    def run_dig_dev(self):
        """Simula ejecución de dig"""
        self.results['tools_results']['dig'] = {
            'status': 'running',
            'progress': 'Consultando registros DNS...'
        }
        
        self.simulate_tool_delay('dig', 1, 2)
        
        # Datos simulados
        dns_data = {
            'A': ['93.184.216.34', '93.184.216.35'],
            'MX': ['10 mail.example.com', '20 mail2.example.com'],
            'NS': ['ns1.example.com', 'ns2.example.com'],
            'TXT': ['v=spf1 include:_spf.example.com ~all']
        }
        
        self.results['tools_results']['dig'] = {
            'status': 'completed',
            'progress': 'Registros DNS obtenidos',
            'output': dns_data
        }

    def run_theharvester_dev(self):
        """Simula ejecución de theHarvester"""
        self.results['tools_results']['theharvester'] = {
            'status': 'running',
            'progress': 'Recolectando emails y subdominios...'
        }
        
        self.simulate_tool_delay('theharvester', 3, 6)
        
        # Datos simulados
        harvester_data = {
            'emails': [
                'admin@' + self.target,
                'info@' + self.target,
                'contact@' + self.target
            ],
            'hosts': [
                'www.' + self.target,
                'mail.' + self.target,
                'ftp.' + self.target,
                'blog.' + self.target
            ]
        }
        
        self.results['tools_results']['theharvester'] = {
            'status': 'completed',
            'progress': f'{len(harvester_data["emails"])} emails y {len(harvester_data["hosts"])} hosts encontrados',
            'output': harvester_data
        }

    def run_assetfinder_dev(self):
        """Simula ejecución de assetfinder"""
        self.results['tools_results']['assetfinder'] = {
            'status': 'running',
            'progress': 'Enumerando subdominios...'
        }
        
        self.simulate_tool_delay('assetfinder', 2, 4)
        
        # Datos simulados
        subdomains = [
            'www.' + self.target,
            'api.' + self.target,
            'cdn.' + self.target,
            'staging.' + self.target,
            'dev.' + self.target
        ]
        
        self.results['tools_results']['assetfinder'] = {
            'status': 'completed',
            'progress': f'{len(subdomains)} subdominios encontrados',
            'output': subdomains
        }

    def run_nuclei_dev(self):
        """Simula ejecución de nuclei"""
        self.results['tools_results']['nuclei'] = {
            'status': 'running',
            'progress': 'Escaneando vulnerabilidades...'
        }
        
        self.simulate_tool_delay('nuclei', 4, 8)
        
        # Datos simulados de vulnerabilidades
        import random
        vulnerabilities = []
        
        if random.choice([True, False]):  # 50% chance de encontrar vulnerabilidades
            vulnerabilities = [
                {
                    'template-id': 'ssl-tls-version',
                    'info': {'name': 'SSL/TLS Version Detection', 'severity': 'info'},
                    'matched-at': f'https://{self.target}',
                    'extracted-results': ['TLSv1.2', 'TLSv1.3']
                },
                {
                    'template-id': 'http-missing-security-headers',
                    'info': {'name': 'Missing Security Headers', 'severity': 'low'},
                    'matched-at': f'https://{self.target}',
                    'extracted-results': ['X-Frame-Options', 'X-Content-Type-Options']
                }
            ]
        
        self.results['tools_results']['nuclei'] = {
            'status': 'completed',
            'progress': f'{len(vulnerabilities)} vulnerabilidades encontradas' if vulnerabilities else 'Sin vulnerabilidades detectadas',
            'output': vulnerabilities
        }

    def run_full_scan(self):
        """Ejecuta el análisis completo simulado"""
        print(f"Iniciando análisis completo simulado para: {self.target}")
        
        try:
            # Lista de herramientas a ejecutar
            tools_to_run = [
                ('whois', self.run_whois_dev),
                ('dig', self.run_dig_dev),
                ('theharvester', self.run_theharvester_dev),
                ('assetfinder', self.run_assetfinder_dev),
                ('nuclei', self.run_nuclei_dev)
            ]

            # Ejecutar cada herramienta
            for tool_name, tool_function in tools_to_run:
                print(f"Ejecutando {tool_name}...")
                try:
                    tool_function()
                    print(f"{tool_name} completado exitosamente")
                except Exception as e:
                    print(f"Error ejecutando {tool_name}: {str(e)}")
                    self.results['tools_results'][tool_name] = {
                        'status': 'error',
                        'errors': str(e),
                        'output': f'Error durante la ejecución: {str(e)}'
                    }

            # Generar resumen
            self.generate_summary()
            
            # Marcar como completado
            self.results['status'] = 'completed'
            self.results['end_time'] = datetime.now().isoformat()
            
            print(f"Análisis terminado para: {self.target} con estado: completed")

        except Exception as e:
            print(f"Error crítico en el análisis: {str(e)}")
            self.results['status'] = 'error'
            self.results['error'] = str(e)
            self.results['end_time'] = datetime.now().isoformat()

    def generate_summary(self):
        """Genera un resumen de los resultados"""
        summary = {
            'total_tools': len(self.results['tools_results']),
            'successful_tools': 0,
            'failed_tools': 0,
            'vulnerabilities_found': 0,
            'subdomains_found': 0,
            'emails_found': 0
        }
        
        for tool_name, tool_result in self.results['tools_results'].items():
            if tool_result.get('status') == 'completed':
                summary['successful_tools'] += 1
            else:
                summary['failed_tools'] += 1
        
        # Contar resultados específicos
        if 'nuclei' in self.results['tools_results']:
            nuclei_output = self.results['tools_results']['nuclei'].get('output', [])
            summary['vulnerabilities_found'] = len(nuclei_output) if isinstance(nuclei_output, list) else 0
        
        if 'assetfinder' in self.results['tools_results']:
            assetfinder_output = self.results['tools_results']['assetfinder'].get('output', [])
            summary['subdomains_found'] = len(assetfinder_output) if isinstance(assetfinder_output, list) else 0
        
        if 'theharvester' in self.results['tools_results']:
            theharvester_output = self.results['tools_results']['theharvester'].get('output', {})
            summary['emails_found'] = len(theharvester_output.get('emails', [])) if isinstance(theharvester_output, dict) else 0
        
        self.results['summary'] = summary
        
        # Generar recomendaciones básicas
        recommendations = []
        if summary['vulnerabilities_found'] > 0:
            recommendations.append("Se encontraron vulnerabilidades. Revisar y corregir los problemas identificados.")
        if summary['emails_found'] > 0:
            recommendations.append("Se encontraron direcciones de email públicas. Considerar medidas anti-spam.")
        if summary['subdomains_found'] > 5:
            recommendations.append("Se encontraron múltiples subdominios. Verificar que todos estén seguros.")
        
        if not recommendations:
            recommendations.append("No se encontraron problemas críticos de seguridad.")
        
        self.results['recommendations'] = recommendations

# Rutas Flask
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/sw.js')
def service_worker():
    return send_file('static/sw.js', mimetype='application/javascript')

@app.route('/scan', methods=['POST'])
def start_scan():
    data = request.get_json()
    target = data.get('target', '').strip()

    if not target:
        return jsonify({'error': 'Target is required'}), 400

    # Validar formato del target - más permisivo
    if not re.match(r'^[a-zA-Z0-9]([a-zA-Z0-9\-._]{0,253}[a-zA-Z0-9])?\.[a-zA-Z]{2,}$', target):
        return jsonify({'error': f'Formato de dominio inválido: {target}. Usa formato como ejemplo.com'}), 400

    # Verificar límite de scans concurrentes
    if len(active_scans) >= MAX_CONCURRENT_SCANS:
        return jsonify({'error': f'Maximum concurrent scans ({MAX_CONCURRENT_SCANS}) reached'}), 429

    # Generar ID único para el análisis
    scan_id = str(uuid.uuid4())

    # Crear scanner de desarrollo
    scanner = SecurityScannerDev(target, scan_id)
    active_scans[scan_id] = scanner

    # Ejecutar análisis en hilo separado
    def run_scan_and_save():
        try:
            scanner.run_full_scan()
            # Guardar resultados en archivo para persistencia
            save_scan_results(scan_id, scanner.results)
            print(f"Scan {scan_id} completado y guardado")
        except Exception as e:
            print(f"Error en scan {scan_id}: {e}")

    thread = threading.Thread(target=run_scan_and_save)
    thread.daemon = True
    thread.start()

    return jsonify({
        'scan_id': scan_id,
        'message': 'Análisis iniciado correctamente'
    })

@app.route('/status/<scan_id>')
def get_scan_status(scan_id):
    # Primero buscar en scans activos
    if scan_id in active_scans:
        scanner = active_scans[scan_id]
        return jsonify(scanner.results)
    
    # Si no está activo, buscar en archivos guardados
    results = load_scan_results(scan_id)
    if results:
        return jsonify(results)
    
    return jsonify({'error': 'Scan not found'}), 404

@app.route('/report/<scan_id>')
def generate_report(scan_id):
    # Buscar en scans activos primero
    results = None
    if scan_id in active_scans:
        scanner = active_scans[scan_id]
        results = scanner.results
    else:
        # Buscar en archivos guardados
        results = load_scan_results(scan_id)
    
    if not results:
        return jsonify({'error': 'Scan not found'}), 404

    # Permitir generar reporte si está completado o completado con errores
    if results['status'] not in ['completed', 'completed_with_errors']:
        return jsonify({'error': 'Scan not completed yet'}), 400

    # Generar reporte HTML
    report_html = render_template('report.html', results=results)

    # Guardar reporte
    target = results.get('target', 'unknown')
    report_filename = f"security_report_{scan_id}_{target}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
    report_path = os.path.join(REPORTS_DIR, report_filename)

    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_html)

    return send_file(report_path, as_attachment=True, download_name=report_filename)

if __name__ == '__main__':
    print("🔧 Security Scanner - Modo Desarrollo Local")
    print("==========================================")
    print("✅ Todas las herramientas simuladas")
    print("✅ Sin dependencias externas")
    print("✅ Perfecto para testing")
    print("")
    print("🌐 Accede a: http://localhost:5000")
    print("⏹️  Para detener: Ctrl+C")
    print("")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
