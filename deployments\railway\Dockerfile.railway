# Railway-optimized Dockerfile for Security Scanner Web Interface
FROM ubuntu:22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-venv \
    dnsutils \
    whois \
    wget \
    curl \
    git \
    build-essential \
    ca-certificates \
    unzip \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Go
ENV GO_VERSION=1.21.5
RUN wget -q https://golang.org/dl/go${GO_VERSION}.linux-amd64.tar.gz \
    && tar -C /usr/local -xzf go${GO_VERSION}.linux-amd64.tar.gz \
    && rm go${GO_VERSION}.linux-amd64.tar.gz

ENV PATH="/usr/local/go/bin:${PATH}"
ENV GOPATH="/go"
ENV PATH="${GOPATH}/bin:${PATH}"

# Install security tools
RUN wget -q https://github.com/projectdiscovery/nuclei/releases/latest/download/nuclei_3.4.4_linux_amd64.zip \
    && unzip nuclei_3.4.4_linux_amd64.zip \
    && mv nuclei /usr/local/bin/ \
    && chmod +x /usr/local/bin/nuclei \
    && rm nuclei_3.4.4_linux_amd64.zip

RUN git clone https://github.com/laramies/theHarvester.git /opt/theHarvester \
    && cd /opt/theHarvester \
    && python3 -m pip install -r requirements.txt \
    && chmod +x theHarvester.py \
    && ln -s /opt/theHarvester/theHarvester.py /usr/local/bin/theharvester

RUN go install github.com/tomnomnom/assetfinder@latest \
    && cp ${GOPATH}/bin/assetfinder /usr/local/bin/

# Create app directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt requirements-docker.txt ./

# Create virtual environment and install dependencies to avoid conflicts
RUN python3 -m venv /opt/venv \
    && /opt/venv/bin/pip install --no-cache-dir --upgrade pip \
    && /opt/venv/bin/pip install --no-cache-dir -r requirements.txt \
    && /opt/venv/bin/pip install --no-cache-dir -r requirements-docker.txt \
    && /opt/venv/bin/pip install --no-cache-dir gunicorn

# Make sure we use venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy application files
COPY . .

# Create reports directory
RUN mkdir -p reports

# Update nuclei templates
RUN timeout 60 nuclei -update-templates -silent || true

# Railway uses PORT environment variable
EXPOSE $PORT

CMD /opt/venv/bin/gunicorn --bind 0.0.0.0:$PORT --workers 2 --timeout 120 app:app
