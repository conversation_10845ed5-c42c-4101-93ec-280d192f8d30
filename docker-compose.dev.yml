# Development override for Security Scanner Web Interface
# Use with: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

version: '3.8'

services:
  security-scanner:
    build:
      context: .
      dockerfile: Dockerfile
      target: base
    volumes:
      # Mount source code for live reloading
      - .:/app
      # Exclude node_modules and other build artifacts
      - /app/__pycache__
      - /app/.git
    environment:
      # Development environment settings
      - FLASK_ENV=development
      - FLASK_DEBUG=true
      - SCANNER_HOST=0.0.0.0
      - SCANNER_PORT=3333
      # Enable hot reloading
      - PYTHONUNBUFFERED=1
      - WERKZEUG_DEBUG_PIN=off
    command: >
      sh -c "
        echo '🔧 Development Mode - Hot Reloading Enabled' &&
        python3 app.py
      "
    # Remove resource limits for development
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
    # Enable debugging ports
    ports:
      - "3333:3333"
      - "5678:5678"  # Debug port for IDE attachment

  # Disable nginx in development
  nginx:
    profiles:
      - disabled
