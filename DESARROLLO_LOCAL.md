# 🏠 DESARROLLO LOCAL - Security Scanner

## ✅ **ENTORNO DE DESARROLLO CONFIGURADO**

### **¿Por qué desarrollo local?**
- 🔧 **Testing completo** sin limitaciones de Railway
- 🚀 **Desarrollo rápido** con hot reloading
- 🧪 **Debugging fácil** con herramientas simuladas
- 💡 **Sin dependencias externas** (nuclei, theharvester, etc.)

## 🚀 **CÓMO USAR EL ENTORNO LOCAL**

### **Opción 1: Script Automático (Recomendado)**
```bash
# Ejecutar desde el directorio del proyecto:
.\start_dev.bat
```

### **Opción 2: Manual**
```bash
# 1. Activar entorno virtual
.\venv\Scripts\activate

# 2. Ejecutar aplicación de desarrollo
python app_dev.py
```

## 🌐 **ACCEDER A LA APLICACIÓN**

**URL Local:** http://localhost:5000

### **Características del Modo Desarrollo:**
- ✅ **Hot Reloading** - Los cambios se reflejan automáticamente
- ✅ **Debug Mode** - Errores detallados en el navegador
- ✅ **Herramientas Simuladas** - No requiere nuclei, theharvester, etc.
- ✅ **Datos Realistas** - Simula resultados reales de las herramientas
- ✅ **Persistencia Local** - Los scans se guardan en `reports/`

## 🧪 **TESTING COMPLETO**

### **1. Probar la Interfaz:**
- ✅ Carga de la página principal
- ✅ Service Worker (sin errores 404)
- ✅ Modo oscuro/claro
- ✅ Responsividad

### **2. Probar Funcionalidad de Scans:**
- ✅ Iniciar un scan con cualquier dominio (ej: `google.com`)
- ✅ Ver progreso en tiempo real
- ✅ Verificar que todas las herramientas "ejecutan"
- ✅ Comprobar estados de las herramientas

### **3. Probar Persistencia:**
- ✅ Completar un scan
- ✅ Recargar la página
- ✅ Verificar que el scan sigue disponible
- ✅ Descargar el reporte

### **4. Probar Múltiples Scans:**
- ✅ Iniciar varios scans simultáneos
- ✅ Verificar límite de concurrencia
- ✅ Comprobar que cada scan es independiente

## 🔧 **HERRAMIENTAS SIMULADAS**

### **whois (1-3 segundos):**
```json
{
  "domain": "example.com",
  "registrar": "Example Registrar Inc.",
  "creation_date": "2020-01-15",
  "expiration_date": "2025-01-15"
}
```

### **dig (1-2 segundos):**
```json
{
  "A": ["*************"],
  "MX": ["10 mail.example.com"],
  "NS": ["ns1.example.com"]
}
```

### **theHarvester (3-6 segundos):**
```json
{
  "emails": ["<EMAIL>", "<EMAIL>"],
  "hosts": ["www.example.com", "mail.example.com"]
}
```

### **assetfinder (2-4 segundos):**
```json
["www.example.com", "api.example.com", "cdn.example.com"]
```

### **nuclei (4-8 segundos):**
```json
[
  {
    "template-id": "ssl-tls-version",
    "info": {"name": "SSL/TLS Version Detection", "severity": "info"},
    "matched-at": "https://example.com"
  }
]
```

## 📊 **VENTAJAS DEL DESARROLLO LOCAL**

### **Para Testing:**
- ✅ **Resultados consistentes** - Siempre los mismos datos simulados
- ✅ **Tiempo controlado** - Cada herramienta toma tiempo predecible
- ✅ **Sin fallos externos** - No depende de servicios externos
- ✅ **Debug completo** - Stack traces detallados

### **Para Desarrollo:**
- ✅ **Cambios instantáneos** - Hot reloading automático
- ✅ **Sin limitaciones** - No hay restricciones de recursos
- ✅ **Testing rápido** - Ciclos de desarrollo más cortos
- ✅ **Logs claros** - Output detallado en consola

## 🔄 **WORKFLOW DE DESARROLLO**

### **1. Desarrollar Localmente:**
```bash
# Iniciar servidor de desarrollo
.\start_dev.bat

# Hacer cambios en el código
# Los cambios se reflejan automáticamente
```

### **2. Testing Local:**
- Probar todas las funcionalidades
- Verificar que no hay errores en consola
- Comprobar que los scans funcionan correctamente
- Validar la generación de reportes

### **3. Cuando Todo Funcione:**
- Hacer commit de los cambios
- Push a GitHub
- Railway hará redeploy automático

## 🚨 **DEBUGGING**

### **Ver Logs en Tiempo Real:**
```bash
# Los logs aparecen en la consola donde ejecutaste start_dev.bat
# Incluyen información detallada de cada herramienta simulada
```

### **Errores Comunes:**
- **Puerto ocupado:** Cambiar puerto en `app_dev.py` línea final
- **Módulos faltantes:** Verificar que el venv esté activado
- **Permisos:** Ejecutar como administrador si es necesario

## 📁 **Archivos de Desarrollo:**

- `app_dev.py` - Aplicación de desarrollo con herramientas simuladas
- `start_dev.bat` - Script de inicio automático
- `venv/` - Entorno virtual de Python
- `reports/` - Reportes generados localmente

---

## 🎯 **¡PERFECTO PARA TESTING COMPLETO!**

**Ahora puedes:**
- ✅ **Probar toda la funcionalidad** sin limitaciones
- ✅ **Desarrollar nuevas características** rápidamente
- ✅ **Debuggear problemas** con información detallada
- ✅ **Validar cambios** antes de desplegar a Railway

**Una vez que todo funcione perfectamente en local, podemos aplicar las correcciones a Railway.**
