#!/bin/bash
# Security Scanner Web Interface - Automated Deployment Script
# Supports multiple cloud platforms

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to show help
show_help() {
    print_color $BLUE "Security Scanner Web Interface - Deployment Script"
    echo "=================================================="
    echo ""
    echo "Usage: ./deploy.sh [PLATFORM] [OPTIONS]"
    echo ""
    echo "Platforms:"
    echo "  railway     Deploy to Railway (recommended for beginners)"
    echo "  render      Deploy to Render"
    echo "  heroku      Deploy to Heroku"
    echo "  fly         Deploy to Fly.io"
    echo "  docker      Build and test Docker image locally"
    echo ""
    echo "Options:"
    echo "  --help      Show this help message"
    echo "  --test      Test deployment locally first"
    echo "  --force     Force deployment without confirmation"
    echo ""
    echo "Examples:"
    echo "  ./deploy.sh railway"
    echo "  ./deploy.sh heroku --test"
    echo "  ./deploy.sh docker"
    echo ""
}

# Function to check prerequisites
check_prerequisites() {
    print_color $BLUE "🔍 Checking prerequisites..."
    
    # Check if git is installed
    if ! command -v git &> /dev/null; then
        print_color $RED "❌ Git is not installed"
        exit 1
    fi
    
    # Check if we're in a git repository
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        print_color $RED "❌ Not in a git repository"
        echo "Please initialize git repository first:"
        echo "  git init"
        echo "  git add ."
        echo "  git commit -m 'Initial commit'"
        exit 1
    fi
    
    print_color $GREEN "✅ Prerequisites check passed"
}

# Function to test Docker build locally
test_docker_build() {
    print_color $BLUE "🔨 Testing Docker build locally..."
    
    if ! command -v docker &> /dev/null; then
        print_color $RED "❌ Docker is not installed"
        echo "Please install Docker first"
        exit 1
    fi
    
    # Build the image
    docker build -t security-scanner:test .
    
    if [ $? -eq 0 ]; then
        print_color $GREEN "✅ Docker build successful"
        
        # Test run
        print_color $BLUE "🧪 Testing container..."
        docker run -d --name security-scanner-test -p 3333:3333 security-scanner:test
        
        # Wait a moment for startup
        sleep 10
        
        # Test health check
        if curl -f http://localhost:3333/ > /dev/null 2>&1; then
            print_color $GREEN "✅ Container health check passed"
        else
            print_color $YELLOW "⚠️  Container health check failed (may be normal during startup)"
        fi
        
        # Cleanup
        docker stop security-scanner-test
        docker rm security-scanner-test
        
        print_color $GREEN "✅ Local Docker test completed"
    else
        print_color $RED "❌ Docker build failed"
        exit 1
    fi
}

# Function to deploy to Railway
deploy_railway() {
    print_color $BLUE "🚂 Deploying to Railway..."
    
    # Check if railway CLI is installed
    if ! command -v railway &> /dev/null; then
        print_color $YELLOW "⚠️  Railway CLI not found. Installing..."
        
        # Install Railway CLI
        if [[ "$OSTYPE" == "darwin"* ]]; then
            brew install railway
        else
            curl -fsSL https://railway.app/install.sh | sh
        fi
    fi
    
    # Login to Railway
    print_color $BLUE "🔐 Please login to Railway..."
    railway login
    
    # Initialize project
    railway init
    
    # Deploy
    print_color $BLUE "🚀 Deploying to Railway..."
    railway up
    
    print_color $GREEN "✅ Deployment to Railway completed!"
    print_color $BLUE "🌐 Your app will be available at the Railway-provided URL"
}

# Function to deploy to Render
deploy_render() {
    print_color $BLUE "🎨 Deploying to Render..."
    
    print_color $YELLOW "📝 Manual steps required for Render deployment:"
    echo ""
    echo "1. Go to https://render.com and sign up/login"
    echo "2. Connect your GitHub repository"
    echo "3. Create a new Web Service"
    echo "4. Select your repository"
    echo "5. Set the following configuration:"
    echo "   - Environment: Docker"
    echo "   - Dockerfile Path: deployments/render/Dockerfile.render"
    echo "   - Build Command: (leave empty)"
    echo "   - Start Command: (leave empty)"
    echo "6. Add environment variables:"
    echo "   - FLASK_ENV=production"
    echo "   - FLASK_DEBUG=false"
    echo "   - MAX_CONCURRENT_SCANS=2"
    echo "7. Deploy!"
    echo ""
    print_color $GREEN "✅ Render deployment configuration ready"
}

# Function to deploy to Heroku
deploy_heroku() {
    print_color $BLUE "🟣 Deploying to Heroku..."
    
    # Check if heroku CLI is installed
    if ! command -v heroku &> /dev/null; then
        print_color $RED "❌ Heroku CLI is not installed"
        echo "Please install Heroku CLI first:"
        echo "  macOS: brew tap heroku/brew && brew install heroku"
        echo "  Windows: winget install Heroku.CLI"
        echo "  Linux: curl https://cli-assets.heroku.com/install.sh | sh"
        exit 1
    fi
    
    # Login to Heroku
    print_color $BLUE "🔐 Please login to Heroku..."
    heroku login
    
    # Create app
    read -p "Enter your Heroku app name (or press Enter for auto-generated): " app_name
    
    if [ -z "$app_name" ]; then
        heroku create
    else
        heroku create $app_name
    fi
    
    # Set stack to container
    heroku stack:set container
    
    # Set environment variables
    print_color $BLUE "⚙️  Setting environment variables..."
    heroku config:set FLASK_ENV=production
    heroku config:set FLASK_DEBUG=false
    heroku config:set MAX_CONCURRENT_SCANS=2
    heroku config:set SCAN_TIMEOUT=300
    
    # Deploy
    print_color $BLUE "🚀 Deploying to Heroku..."
    git push heroku main
    
    # Open app
    heroku open
    
    print_color $GREEN "✅ Deployment to Heroku completed!"
}

# Function to deploy to Fly.io
deploy_fly() {
    print_color $BLUE "🪰 Deploying to Fly.io..."
    
    # Check if flyctl is installed
    if ! command -v flyctl &> /dev/null; then
        print_color $YELLOW "⚠️  Fly CLI not found. Installing..."
        curl -L https://fly.io/install.sh | sh
        export PATH="$HOME/.fly/bin:$PATH"
    fi
    
    # Login to Fly
    print_color $BLUE "🔐 Please login to Fly.io..."
    flyctl auth login
    
    # Launch app
    print_color $BLUE "🚀 Launching app on Fly.io..."
    flyctl launch --config deployments/fly/fly.toml
    
    print_color $GREEN "✅ Deployment to Fly.io completed!"
}

# Main execution
case "${1:-help}" in
    "railway")
        check_prerequisites
        if [[ "$2" == "--test" ]]; then
            test_docker_build
        fi
        deploy_railway
        ;;
    "render")
        check_prerequisites
        if [[ "$2" == "--test" ]]; then
            test_docker_build
        fi
        deploy_render
        ;;
    "heroku")
        check_prerequisites
        if [[ "$2" == "--test" ]]; then
            test_docker_build
        fi
        deploy_heroku
        ;;
    "fly")
        check_prerequisites
        if [[ "$2" == "--test" ]]; then
            test_docker_build
        fi
        deploy_fly
        ;;
    "docker")
        test_docker_build
        ;;
    "help"|*)
        show_help
        ;;
esac
