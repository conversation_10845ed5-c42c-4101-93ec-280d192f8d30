# Security Scanner Web Interface - Docker Setup Script for Windows
# PowerShell script to set up and run the Docker environment

param(
    [switch]$Build,
    [switch]$Run,
    [switch]$Dev,
    [switch]$Stop,
    [switch]$Clean,
    [switch]$Help
)

# Colors for output
$Red = "`e[31m"
$Green = "`e[32m"
$Yellow = "`e[33m"
$Blue = "`e[34m"
$Reset = "`e[0m"

function Write-ColorOutput {
    param($Color, $Message)
    Write-Host "$Color$Message$Reset"
}

function Show-Help {
    Write-ColorOutput $Blue "Security Scanner Web Interface - Docker Setup"
    Write-Host "=============================================="
    Write-Host ""
    Write-Host "Usage: .\docker-setup.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Build    Build the Docker image"
    Write-Host "  -Run      Run the application in production mode"
    Write-Host "  -Dev      Run the application in development mode"
    Write-Host "  -Stop     Stop all containers"
    Write-Host "  -Clean    Clean up containers and images"
    Write-Host "  -Help     Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\docker-setup.ps1 -Build"
    Write-Host "  .\docker-setup.ps1 -Run"
    Write-Host "  .\docker-setup.ps1 -Dev"
    Write-Host ""
}

function Test-DockerInstallation {
    Write-ColorOutput $Blue "🔍 Checking Docker installation..."
    
    # Check if Docker is installed
    try {
        $dockerVersion = docker --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput $Green "✅ Docker is installed: $dockerVersion"
        } else {
            throw "Docker not found"
        }
    } catch {
        Write-ColorOutput $Red "❌ Docker is not installed or not in PATH"
        Write-Host ""
        Write-Host "Please install Docker Desktop for Windows:"
        Write-Host "1. Download from: https://www.docker.com/products/docker-desktop"
        Write-Host "2. Install and restart your computer"
        Write-Host "3. Start Docker Desktop"
        Write-Host "4. Run this script again"
        exit 1
    }
    
    # Check if Docker is running
    try {
        docker info >$null 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput $Green "✅ Docker is running"
        } else {
            throw "Docker not running"
        }
    } catch {
        Write-ColorOutput $Red "❌ Docker is not running"
        Write-Host ""
        Write-Host "Please start Docker Desktop:"
        Write-Host "1. Open Docker Desktop application"
        Write-Host "2. Wait for it to start (whale icon in system tray)"
        Write-Host "3. Run this script again"
        exit 1
    }
    
    # Check Docker Compose
    try {
        $composeVersion = docker-compose --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput $Green "✅ Docker Compose is available: $composeVersion"
        } else {
            throw "Docker Compose not found"
        }
    } catch {
        Write-ColorOutput $Yellow "⚠️  Docker Compose not found, trying 'docker compose'..."
        try {
            docker compose version >$null 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput $Green "✅ Docker Compose (v2) is available"
                # Use docker compose instead of docker-compose
                Set-Alias -Name docker-compose -Value "docker compose" -Scope Global
            } else {
                throw "Docker Compose v2 not found"
            }
        } catch {
            Write-ColorOutput $Red "❌ Docker Compose is not available"
            Write-Host "Please update Docker Desktop to get Docker Compose"
            exit 1
        }
    }
}

function Build-Image {
    Write-ColorOutput $Blue "🔨 Building Security Scanner Docker image..."
    
    # Check if Dockerfile exists
    if (-not (Test-Path "Dockerfile")) {
        Write-ColorOutput $Red "❌ Dockerfile not found in current directory"
        Write-Host "Please run this script from the security_scanner_web directory"
        exit 1
    }
    
    # Build the image
    docker-compose build --no-cache
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput $Green "✅ Docker image built successfully"
    } else {
        Write-ColorOutput $Red "❌ Failed to build Docker image"
        exit 1
    }
}

function Start-Application {
    param([bool]$DevMode = $false)
    
    if ($DevMode) {
        Write-ColorOutput $Blue "🔧 Starting Security Scanner in development mode..."
        docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
    } else {
        Write-ColorOutput $Blue "🚀 Starting Security Scanner in production mode..."
        docker-compose up -d
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput $Green "✅ Application started successfully"
            Write-Host ""
            Write-Host "🌐 Access the application at: http://localhost:3333"
            Write-Host "📊 View logs with: docker-compose logs -f"
            Write-Host "⏹️  Stop with: docker-compose down"
        } else {
            Write-ColorOutput $Red "❌ Failed to start application"
            exit 1
        }
    }
}

function Stop-Application {
    Write-ColorOutput $Blue "⏹️  Stopping Security Scanner containers..."
    docker-compose down
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput $Green "✅ Containers stopped successfully"
    } else {
        Write-ColorOutput $Yellow "⚠️  Some containers may still be running"
    }
}

function Clean-Environment {
    Write-ColorOutput $Blue "🧹 Cleaning up Docker environment..."
    docker-compose down -v --rmi all
    docker system prune -f
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput $Green "✅ Environment cleaned successfully"
    } else {
        Write-ColorOutput $Yellow "⚠️  Some resources may not have been cleaned"
    }
}

# Main execution
if ($Help) {
    Show-Help
    exit 0
}

# Test Docker installation
Test-DockerInstallation

# Execute requested action
if ($Build) {
    Build-Image
} elseif ($Run) {
    Start-Application -DevMode $false
} elseif ($Dev) {
    Start-Application -DevMode $true
} elseif ($Stop) {
    Stop-Application
} elseif ($Clean) {
    Clean-Environment
} else {
    Write-ColorOutput $Yellow "⚠️  No action specified. Use -Help to see available options."
    Show-Help
}
