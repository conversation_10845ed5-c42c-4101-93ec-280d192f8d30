#!/bin/bash
set -e

# Security Scanner Web Interface - Docker Entrypoint
# Handles initialization and cross-platform compatibility

echo "🐳 Security Scanner Web Interface - Docker Container"
echo "=================================================="

# Function to check if a tool is available
check_tool() {
    local tool=$1
    local version_cmd=$2
    
    if command -v "$tool" >/dev/null 2>&1; then
        echo "✅ $tool: Available"
        if [ -n "$version_cmd" ]; then
            $version_cmd 2>/dev/null | head -1 || echo "   (version check failed)"
        fi
        return 0
    else
        echo "❌ $tool: Not found"
        return 1
    fi
}

# Verify all security tools are available
echo ""
echo "🔍 Verifying security tools..."
tools_available=0
total_tools=5

check_tool "nuclei" "nuclei -version" && ((tools_available++))
check_tool "theharvester" && ((tools_available++))
check_tool "assetfinder" && ((tools_available++))
check_tool "dig" "dig -v" && ((tools_available++))
check_tool "whois" "whois --version" && ((tools_available++))

echo ""
echo "📊 Tools available: $tools_available/$total_tools"

if [ $tools_available -lt 4 ]; then
    echo "⚠️  Warning: Some tools are missing. Functionality may be limited."
else
    echo "✅ All essential tools are available!"
fi

# Create reports directory if it doesn't exist
mkdir -p /app/reports
echo "📁 Reports directory ready: /app/reports"

# Set proper permissions for reports directory
chmod 755 /app/reports

# Update Nuclei templates if needed (in background to avoid startup delay)
echo "🔄 Updating Nuclei templates in background..."
(nuclei -update-templates -silent >/dev/null 2>&1 &)

# Handle environment variables
export FLASK_ENV=${FLASK_ENV:-production}
export FLASK_DEBUG=${FLASK_DEBUG:-false}
export SCANNER_HOST=${SCANNER_HOST:-0.0.0.0}
export SCANNER_PORT=${SCANNER_PORT:-3333}

echo ""
echo "🌐 Starting Security Scanner Web Interface..."
echo "   Host: $SCANNER_HOST"
echo "   Port: $SCANNER_PORT"
echo "   Environment: $FLASK_ENV"
echo ""
echo "🔗 Access the application at: http://localhost:$SCANNER_PORT"
echo "⏹️  To stop the container: docker stop <container_name>"
echo ""
echo "=================================================="

# Execute the main command
exec "$@"
