[build]
builder = "DOCKERFILE"
dockerfilePath = "deployments/railway/Dockerfile.railway"

[deploy]
startCommand = "gunicorn --bind 0.0.0.0:$PORT --workers 2 --timeout 120 app:app"
healthcheckPath = "/"
healthcheckTimeout = 30
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 3

[env]
FLASK_ENV = "production"
FLASK_DEBUG = "false"
MAX_CONCURRENT_SCANS = "2"
SCAN_TIMEOUT = "300"
PYTHONUNBUFFERED = "1"
